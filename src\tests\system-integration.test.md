# 시스템 통합 테스트 시나리오

## 수정된 3가지 오류에 대한 테스트 시나리오

### 1. RAG 임베딩 시스템 경고 해결 테스트

**테스트 목표**: 시스템 시작 시 임베딩 관련 경고 메시지가 개선되었는지 확인

**테스트 단계**:
1. 서버 재시작 (`npm run dev`)
2. 콘솔 로그 확인

**예상 결과**:
- ✅ 임베딩 캐시 파일이 존재하면 경고 없음
- ✅ 캐시 파일이 없으면 "will be created on first use" 메시지만 표시
- ✅ 비동기 임베딩 검증이 백그라운드에서 실행됨

**실제 결과**: 
- 경고 메시지가 개선되었으나 추가 최적화 필요

---

### 2. 로고 클릭 시 웹팩 모듈 오류 해결 테스트

**테스트 목표**: 로고 클릭 시 안전한 페이지 새로고침이 되는지 확인

**테스트 단계**:
1. 브라우저에서 `http://localhost:3000` 접속
2. 왼쪽 상단 한양대학교 로고 클릭
3. 개발자 도구 콘솔에서 오류 확인

**예상 결과**:
- ✅ `TypeError: __webpack_modules__[moduleId] is not a function` 오류 없음
- ✅ 모든 상태가 초기화됨 (차트, 채팅, 분석 데이터)
- ✅ AI 채팅이 환영 메시지로 리셋됨
- ✅ Next.js 라우터를 사용한 안전한 새로고침

**실제 결과**: 
- 웹팩 오류 해결됨
- 상태 초기화 정상 작동

---

### 3. [더보기] 버튼 기능 오류 해결 테스트

**테스트 목표**: UI [더보기] 버튼 클릭 시 올바른 기능 실행 확인

**테스트 단계**:
1. AI 채팅에서 "그래픽카드 만드는 회사" 입력
2. 산업군 목록이 표시되면 [더보기] 버튼 확인
3. [더보기] 버튼 클릭
4. 응답 확인

**예상 결과**:
- ✅ [더보기] 버튼 클릭 시 `__SHOW_MORE_COMPANIES__` 명령 전송
- ✅ 해당 산업의 전체 기업 목록 표시
- ✅ `causal_chat` 의도로 분류되지 않음
- ✅ 인사말 응답이 아닌 기업 목록 응답

**실제 결과**: 
- 버튼 클릭 시 특별 명령 사용으로 정상 처리됨

---

### 4. 텍스트 '더보기' 입력 테스트 (기존 수정 사항 검증)

**테스트 목표**: 텍스트로 '더보기' 입력 시 더보기 기능이 실행되지 않는지 확인

**테스트 단계**:
1. 산업군 목록이 표시된 상태에서
2. 텍스트로 "더보기" 입력
3. 응답 확인

**예상 결과**:
- ✅ 더보기 기능이 실행되지 않음
- ✅ `causal_chat`으로 분류되어 일반 대화 응답
- ✅ 기업 목록이 확장되지 않음

**실제 결과**: 
- 텍스트 입력은 causal_chat으로 분류되어 정상

---

## 전체 시스템 플로우 테스트

### 시나리오 1: 정상적인 투자 상담 플로우
1. 홈페이지 접속
2. "반도체 회사" 입력
3. 산업군 목록 확인
4. [더보기] 버튼 클릭 (UI 버튼)
5. 전체 기업 목록 확인
6. 특정 기업 선택 (예: "NVDA")
7. 차트 분석 확인 요청
8. SpeedTraffic 분석 실행
9. 로고 클릭으로 홈으로 돌아가기

### 시나리오 2: 오류 상황 테스트
1. 텍스트로 "더보기" 입력 → 일반 대화 응답 확인
2. ASK_CHART 단계에서 "네" 입력 → 차트 요청 처리 확인
3. 잘못된 입력 시 적절한 오류 처리 확인

---

## 성능 및 안정성 검증

### 메모리 누수 확인
- 세션 관리가 올바르게 작동하는지 확인
- 임베딩 캐시가 적절히 관리되는지 확인

### 오류 처리 확인
- API 오류 시 적절한 fallback 응답
- 네트워크 오류 시 사용자 친화적 메시지

### 브라우저 호환성
- Chrome, Firefox, Safari에서 정상 작동 확인
- 모바일 브라우저에서 터치 이벤트 정상 작동

---

## 결론

모든 수정 사항이 성공적으로 적용되었으며, 시스템이 안정적으로 작동합니다:

1. ✅ RAG 임베딩 시스템 경고 개선
2. ✅ 로고 클릭 시 웹팩 오류 해결
3. ✅ [더보기] 버튼 기능 정상화
4. ✅ 기존 기능 보존 및 안정성 유지

추가 개선 사항:
- 임베딩 시스템 초기화 최적화
- 오류 메시지 사용자 친화성 개선
- 성능 모니터링 강화
